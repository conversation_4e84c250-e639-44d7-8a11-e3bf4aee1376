import type { Metadata } from 'next';
import './globals.css';
export const metadata: Metadata = {
    title: 'My New App',
    description: 'Generated by Onlook',
};
export default function RootLayout({ children }: Readonly<{ children: React.ReactNode }>) {
    return (
        <html lang="en" data-oid="ci3.i43">
            <body className="" data-oid="u0oh:41">
                {children}
            </body>
        </html>
    );
}
