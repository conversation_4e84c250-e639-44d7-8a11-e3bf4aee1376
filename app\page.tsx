'use client';

import { useState } from 'react';

export default function Page() {
    const [copied, setCopied] = useState(false);

    const robloxScript = `-- Roblox Game Scanner Script
-- Scans and copies game information to clipboard progressively

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")

-- Create progress indicator GUI
local screenGui = Instance.new("ScreenGui")
screenGui.Name = "GameScannerGUI"
screenGui.Parent = Players.LocalPlayer:WaitForChild("PlayerGui")

local progressFrame = Instance.new("Frame")
progressFrame.Size = UDim2.new(0, 200, 0, 20)
progressFrame.Position = UDim2.new(1, -220, 0, 20)
progressFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
progressFrame.BorderSizePixel = 0
progressFrame.Parent = screenGui

local progressBar = Instance.new("Frame")
progressBar.Size = UDim2.new(0, 0, 1, 0)
progressBar.Position = UDim2.new(0, 0, 0, 0)
progressBar.BackgroundColor3 = Color3.fromRGB(0, 255, 100)
progressBar.BorderSizePixel = 0
progressBar.Parent = progressFrame

local progressText = Instance.new("TextLabel")
progressText.Size = UDim2.new(1, 0, 1, 0)
progressText.Position = UDim2.new(0, 0, 0, 0)
progressText.BackgroundTransparency = 1
progressText.Text = "Starting scan..."
progressText.TextColor3 = Color3.fromRGB(255, 255, 255)
progressText.TextScaled = true
progressText.Font = Enum.Font.SourceSansBold
progressText.Parent = progressFrame

-- Function to serialize object data
local function serializeObject(obj, depth)
    depth = depth or 0
    if depth > 5 then return "-- Max depth reached" end
    
    local result = ""
    local indent = string.rep("  ", depth)
    
    result = result .. indent .. "-- " .. obj.Name .. " (" .. obj.ClassName .. ")\\n"
    
    -- Get properties
    local success, properties = pcall(function()
        local props = {}
        for _, prop in pairs({"Name", "ClassName", "Parent"}) do
            if obj[prop] then
                table.insert(props, prop .. ": " .. tostring(obj[prop]))
            end
        end
        return props
    end)
    
    if success then
        for _, prop in pairs(properties) do
            result = result .. indent .. "-- " .. prop .. "\\n"
        end
    end
    
    return result
end

-- Function to scan service
local function scanService(service, serviceName)
    local data = "-- === " .. serviceName .. " ===\\n"
    
    local function scanChildren(parent, depth)
        depth = depth or 0
        if depth > 3 then return "" end
        
        local childData = ""
        for _, child in pairs(parent:GetChildren()) do
            childData = childData .. serializeObject(child, depth)
            if #child:GetChildren() > 0 then
                childData = childData .. scanChildren(child, depth + 1)
            end
        end
        return childData
    end
    
    data = data .. scanChildren(service)
    return data
end

-- Services to scan
local servicesToScan = {
    {game.Workspace, "Workspace"},
    {game.Players, "Players"},
    {game.ReplicatedStorage, "ReplicatedStorage"},
    {game.ReplicatedFirst, "ReplicatedFirst"},
    {game.ServerStorage, "ServerStorage"},
    {game.ServerScriptService, "ServerScriptService"},
    {game.StarterGui, "StarterGui"},
    {game.StarterPack, "StarterPack"},
    {game.StarterPlayer, "StarterPlayer"},
    {game.SoundService, "SoundService"},
    {game.Lighting, "Lighting"},
    {game.MaterialService, "MaterialService"},
    {game.Teams, "Teams"}
}

-- Main scanning function
local function startScan()
    local totalServices = #servicesToScan
    local currentService = 0
    
    local function scanNext()
        currentService = currentService + 1
        
        if currentService > totalServices then
            progressText.Text = "Scan Complete!"
            progressBar.BackgroundColor3 = Color3.fromRGB(0, 255, 0)
            wait(2)
            screenGui:Destroy()
            return
        end
        
        local service, serviceName = servicesToScan[currentService][1], servicesToScan[currentService][2]
        progressText.Text = "Scanning " .. serviceName .. "..."
        
        -- Update progress bar
        local progress = currentService / totalServices
        local tween = TweenService:Create(progressBar, TweenInfo.new(0.3), {Size = UDim2.new(progress, 0, 1, 0)})
        tween:Play()
        
        -- Scan service
        local success, data = pcall(function()
            return scanService(service, serviceName)
        end)
        
        if success then
            -- Copy to clipboard (this requires a clipboard service or external tool)
            setclipboard(data)
            print("Copied " .. serviceName .. " data to clipboard")
        else
            print("Failed to scan " .. serviceName)
        end
        
        -- Wait before next scan to avoid crashes
        wait(1)
        scanNext()
    end
    
    scanNext()
end

-- Start the scan
startScan()

print("Game scanner started! Check top-right corner for progress.")`;

    const copyToClipboard = async () => {
        try {
            await navigator.clipboard.writeText(robloxScript);
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        } catch (err) {
            console.error('Failed to copy: ', err);
        }
    };

    return (
        <div
            className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white"
            data-oid="x485b22"
        >
            {/* Header */}
            <header
                className="border-b border-purple-500/20 bg-black/20 backdrop-blur-sm"
                data-oid="nn5e512"
            >
                <div className="max-w-6xl mx-auto px-6 py-4" data-oid="ty6iwal">
                    <div className="flex items-center justify-between" data-oid="orjkc::">
                        <div className="flex items-center space-x-3" data-oid="7.q3-c4">
                            <div
                                className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center"
                                data-oid="i24w3y4"
                            >
                                <span className="text-white font-bold text-sm" data-oid="3-_q90e">
                                    R
                                </span>
                            </div>
                            <h1
                                className="text-xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent"
                                data-oid="zwfogm6"
                            >
                                Roblox Game Scanner
                            </h1>
                        </div>
                        <div className="text-sm text-purple-300" data-oid="6migbsz">
                            v1.0 | Safe & Efficient
                        </div>
                    </div>
                </div>
            </header>

            {/* Main Content */}
            <main className="max-w-6xl mx-auto px-6 py-12" data-oid="8y6bxx1">
                {/* Hero Section */}
                <div className="text-center mb-12" data-oid="ksjch2e">
                    <h2
                        className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-purple-400 via-pink-400 to-purple-400 bg-clip-text text-transparent"
                        data-oid="-ig3gah"
                    >
                        Advanced Game Scanner
                    </h2>
                    <p
                        className="text-xl text-purple-200 max-w-3xl mx-auto leading-relaxed"
                        data-oid="zmx3xj4"
                    >
                        Comprehensive Roblox Lua script that safely scans and copies game
                        information including Workspace, Players, ReplicatedStorage, and all other
                        services to your clipboard with crash prevention and progress tracking.
                    </p>
                </div>

                {/* Features Grid */}
                <div className="grid md:grid-cols-3 gap-6 mb-12" data-oid="3ar0ylx">
                    <div
                        className="bg-black/30 backdrop-blur-sm border border-purple-500/20 rounded-xl p-6"
                        data-oid="hitfv21"
                    >
                        <div
                            className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center mb-4"
                            data-oid="m9d-24g"
                        >
                            <svg
                                className="w-6 h-6 text-white"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                                data-oid="036vp:s"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                    data-oid="bn2y:72"
                                />
                            </svg>
                        </div>
                        <h3
                            className="text-lg font-semibold text-green-400 mb-2"
                            data-oid="drzra8_"
                        >
                            Crash Prevention
                        </h3>
                        <p className="text-purple-200 text-sm" data-oid="npva99k">
                            Progressive scanning with delays between operations to prevent game
                            crashes and ensure stability.
                        </p>
                    </div>

                    <div
                        className="bg-black/30 backdrop-blur-sm border border-purple-500/20 rounded-xl p-6"
                        data-oid="uoevg6b"
                    >
                        <div
                            className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center mb-4"
                            data-oid="ffpldy."
                        >
                            <svg
                                className="w-6 h-6 text-white"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                                data-oid="8pzdzr1"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M13 10V3L4 14h7v7l9-11h-7z"
                                    data-oid="5z.s4d-"
                                />
                            </svg>
                        </div>
                        <h3 className="text-lg font-semibold text-blue-400 mb-2" data-oid="_yrcaho">
                            Progress Tracking
                        </h3>
                        <p className="text-purple-200 text-sm" data-oid="0fd_atx">
                            Real-time progress bar in the top-right corner shows scanning status and
                            completion percentage.
                        </p>
                    </div>

                    <div
                        className="bg-black/30 backdrop-blur-sm border border-purple-500/20 rounded-xl p-6"
                        data-oid="8jpgmq."
                    >
                        <div
                            className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mb-4"
                            data-oid="g0djj85"
                        >
                            <svg
                                className="w-6 h-6 text-white"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                                data-oid="b44y8kb"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                                    data-oid="-ud_0l0"
                                />
                            </svg>
                        </div>
                        <h3
                            className="text-lg font-semibold text-purple-400 mb-2"
                            data-oid="mz7c1gp"
                        >
                            Complete Coverage
                        </h3>
                        <p className="text-purple-200 text-sm" data-oid="1uvfx-:">
                            Scans all major services: Workspace, Players, ReplicatedStorage,
                            ServerStorage, and more.
                        </p>
                    </div>
                </div>

                {/* Script Section */}
                <div
                    className="bg-black/40 backdrop-blur-sm border border-purple-500/20 rounded-xl overflow-hidden"
                    data-oid="no2-3om"
                >
                    <div
                        className="flex items-center justify-between p-4 border-b border-purple-500/20"
                        data-oid="kwf_3pz"
                    >
                        <div className="flex items-center space-x-3" data-oid="9v.zo1y">
                            <div className="flex space-x-2" data-oid="vv3cyj0">
                                <div
                                    className="w-3 h-3 bg-red-500 rounded-full"
                                    data-oid="axg2uvw"
                                ></div>
                                <div
                                    className="w-3 h-3 bg-yellow-500 rounded-full"
                                    data-oid="rduypye"
                                ></div>
                                <div
                                    className="w-3 h-3 bg-green-500 rounded-full"
                                    data-oid="j08f-ua"
                                ></div>
                            </div>
                            <span className="text-purple-300 font-mono text-sm" data-oid="u6e9nok">
                                game_scanner.lua
                            </span>
                        </div>
                        <button
                            onClick={copyToClipboard}
                            className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                                copied
                                    ? 'bg-green-600 text-white'
                                    : 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-500 hover:to-pink-500 text-white'
                            }`}
                            data-oid="45jap18"
                        >
                            {copied ? (
                                <span className="flex items-center space-x-2" data-oid="xu1m5nz">
                                    <svg
                                        className="w-4 h-4"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                        data-oid="tuxlene"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M5 13l4 4L19 7"
                                            data-oid="9-vd818"
                                        />
                                    </svg>
                                    <span data-oid="c4x2-qa">Copied!</span>
                                </span>
                            ) : (
                                <span className="flex items-center space-x-2" data-oid="8ivlxn-">
                                    <svg
                                        className="w-4 h-4"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                        data-oid="j_:zqyd"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                                            data-oid="1-cdd_9"
                                        />
                                    </svg>
                                    <span data-oid="_i2f6c3">Copy Script</span>
                                </span>
                            )}
                        </button>
                    </div>
                    <div className="p-6 max-h-96 overflow-y-auto" data-oid="c9jjg9p">
                        <pre
                            className="text-sm text-purple-100 font-mono leading-relaxed whitespace-pre-wrap"
                            data-oid="s08j50y"
                        >
                            {robloxScript}
                        </pre>
                    </div>
                </div>

                {/* Instructions */}
                <div
                    className="mt-12 bg-gradient-to-r from-purple-900/50 to-pink-900/50 border border-purple-500/30 rounded-xl p-6"
                    data-oid="5tx9q5_"
                >
                    <h3
                        className="text-xl font-semibold text-purple-300 mb-4 flex items-center"
                        data-oid="a_po0d8"
                    >
                        <svg
                            className="w-5 h-5 mr-2"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            data-oid="9fefjv6"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                data-oid="a5ldxrq"
                            />
                        </svg>
                        How to Use
                    </h3>
                    <ol className="space-y-2 text-purple-200" data-oid="v7moe7_">
                        <li className="flex items-start" data-oid="jzozpbv">
                            <span
                                className="bg-purple-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5"
                                data-oid="3kmh4:j"
                            >
                                1
                            </span>
                            Copy the script using the button above
                        </li>
                        <li className="flex items-start" data-oid="3od215:">
                            <span
                                className="bg-purple-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5"
                                data-oid="hz5rxd."
                            >
                                2
                            </span>
                            Open Roblox Studio or use a script executor in-game
                        </li>
                        <li className="flex items-start" data-oid="xv4t8yo">
                            <span
                                className="bg-purple-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5"
                                data-oid="1un3buw"
                            >
                                3
                            </span>
                            Paste and run the script
                        </li>
                        <li className="flex items-start" data-oid=".p0yn81">
                            <span
                                className="bg-purple-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5"
                                data-oid="_hapc:c"
                            >
                                4
                            </span>
                            Watch the progress bar in the top-right corner
                        </li>
                        <li className="flex items-start" data-oid="9o8:nid">
                            <span
                                className="bg-purple-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5"
                                data-oid="0d4v1py"
                            >
                                5
                            </span>
                            Data will be copied to your clipboard service by service
                        </li>
                    </ol>
                </div>
            </main>

            {/* Footer */}
            <footer
                className="border-t border-purple-500/20 bg-black/20 backdrop-blur-sm mt-12"
                data-oid="xezpbno"
            >
                <div className="max-w-6xl mx-auto px-6 py-8 text-center" data-oid="3tvf8e7">
                    <p className="text-purple-300" data-oid="2x0tc_g">
                        ⚠️ Use responsibly and in accordance with Roblox Terms of Service
                    </p>
                </div>
            </footer>
        </div>
    );
}
