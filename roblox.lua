-- Enhanced Roblox Game Scanner Script
-- Deep scans and copies ALL game information to clipboard progressively with maximum detail extraction
-- Goes infinitely deep into all objects and extracts ALL properties

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")
local HttpService = game:GetService("HttpService")

-- Variable to store accumulated clipboard data
local accumulatedData = ""
local separator = "\n\n" .. string.rep("🔥", 50) .. "\n\n"
local objectCount = 0
local maxObjects = 100000 -- INCREASED limit for MAXIMUM extraction
local startTime = tick() -- Track scanning time

-- Create progress indicator GUI
local screenGui = Instance.new("ScreenGui")
screenGui.Name = "GameScannerGUI"
screenGui.Parent = Players.LocalPlayer:WaitForChild("PlayerGui")

local progressFrame = Instance.new("Frame")
progressFrame.Size = UDim2.new(0, 200, 0, 20)
progressFrame.Position = UDim2.new(1, -220, 0, 20)
progressFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
progressFrame.BorderSizePixel = 0
progressFrame.Parent = screenGui

local progressBar = Instance.new("Frame")
progressBar.Size = UDim2.new(0, 0, 1, 0)
progressBar.Position = UDim2.new(0, 0, 0, 0)
progressBar.BackgroundColor3 = Color3.fromRGB(0, 255, 100)
progressBar.BorderSizePixel = 0
progressBar.Parent = progressFrame

local progressText = Instance.new("TextLabel")
progressText.Size = UDim2.new(1, 0, 1, 0)
progressText.Position = UDim2.new(0, 0, 0, 0)
progressText.BackgroundTransparency = 1
progressText.Text = "Starting scan..."
progressText.TextColor3 = Color3.fromRGB(255, 255, 255)
progressText.TextScaled = true
progressText.Font = Enum.Font.SourceSansBold
progressText.Parent = progressFrame

-- ULTIMATE function to get EVERY SINGLE PROPERTY of an object
local function getAllProperties(obj)
    local properties = {}

    -- MASSIVE list of ALL possible Roblox properties
    local allPossibleProps = {
        -- Basic Instance properties
        "Name", "ClassName", "Parent", "Archivable", "RobloxLocked",

        -- BasePart properties (Parts, MeshParts, etc.)
        "Position", "Size", "Rotation", "Orientation", "CFrame", "Material", "BrickColor", "Color",
        "Transparency", "Reflectance", "CanCollide", "Anchored", "Shape", "TopSurface", "BottomSurface",
        "LeftSurface", "RightSurface", "FrontSurface", "BackSurface", "Velocity", "AngularVelocity",
        "AssemblyLinearVelocity", "AssemblyAngularVelocity", "AssemblyMass", "AssemblyRootPart",
        "AssemblyCenterOfMass", "CustomPhysicalProperties", "Elasticity", "Friction", "ElasticityWeight",
        "FrictionWeight", "Density", "ReceiveAge", "LocalTransparencyModifier", "UsePartColor",

        -- MeshPart specific
        "MeshId", "TextureID", "MeshSize", "Offset", "Scale", "VertexColor",

        -- GUI properties
        "Size", "Position", "BackgroundColor3", "BackgroundTransparency", "BorderColor3", "BorderSizePixel",
        "Rotation", "Visible", "ZIndex", "ClipsDescendants", "Active", "Selectable", "AutomaticSize",
        "LayoutOrder", "SizeConstraint", "NextSelectionUp", "NextSelectionDown", "NextSelectionLeft", "NextSelectionRight",

        -- Text properties
        "Text", "TextColor3", "TextSize", "Font", "TextWrapped", "TextScaled", "TextStrokeTransparency",
        "TextStrokeColor3", "TextXAlignment", "TextYAlignment", "RichText", "TextBounds", "TextFits",
        "LineHeight", "MaxVisibleGraphemes", "TextDirection", "TextTruncate",

        -- Image properties
        "Image", "ImageColor3", "ImageTransparency", "ScaleType", "SliceCenter", "SliceScale",
        "TileSize", "ImageRectOffset", "ImageRectSize", "ResampleMode",

        -- Button properties
        "AutoButtonColor", "Modal", "Selected", "Style",

        -- ScrollingFrame properties
        "ScrollBarThickness", "ScrollBarImageColor3", "CanvasSize", "ScrollingDirection", "ScrollingEnabled",
        "ElasticBehavior", "ScrollBarImageTransparency", "TopImage", "MidImage", "BottomImage",
        "HorizontalScrollBarInset", "VerticalScrollBarInset", "VerticalScrollBarPosition",

        -- Frame properties
        "Style", "BorderMode",

        -- ScreenGui properties
        "DisplayOrder", "IgnoreGuiInset", "ResetOnSpawn", "ZIndexBehavior", "ScreenInsets", "SelectionGroup",
        "SelectionBehaviorUp", "SelectionBehaviorDown", "SelectionBehaviorLeft", "SelectionBehaviorRight",

        -- SurfaceGui properties
        "Face", "CanvasSize", "LightInfluence", "Brightness", "ToolPunchThroughDistance", "PixelsPerStud",
        "SizingMode", "ZOffset", "ClipsDescendants", "AlwaysOnTop",

        -- Script properties
        "Source", "Disabled", "LinkedSource", "RunContext",

        -- Sound properties
        "SoundId", "Volume", "Pitch", "PlaybackSpeed", "Looped", "Playing", "TimePosition", "EmitterSize",
        "MinDistance", "MaxDistance", "RollOffMode", "SoundGroup", "PlayOnRemove", "IsLoaded", "IsPaused",

        -- Light properties
        "Brightness", "Color", "Range", "Shadows", "Angle", "Face", "Enabled",

        -- Camera properties
        "CameraType", "FieldOfView", "Focus", "CameraSubject", "HeadLocked", "HeadScale", "DiagonalFieldOfView",

        -- Humanoid properties
        "Health", "MaxHealth", "WalkSpeed", "JumpPower", "JumpHeight", "DisplayDistanceType",
        "HealthDisplayDistance", "NameDisplayDistance", "AutoRotate", "AutoJumpEnabled", "Sit", "Jump",
        "PlatformStand", "RequiresNeck", "RigType", "HipHeight", "UseJumpPower", "BreakJointsOnDeath",
        "MaxSlopeAngle", "CameraOffset", "WalkToPart", "WalkToPoint",

        -- Model properties
        "PrimaryPart", "WorldPivot", "ModelMeshSize", "ModelMeshCFrame", "ModelMeshData",

        -- Value object properties
        "Value",

        -- Decal properties
        "Texture", "Face", "Transparency", "Color3", "ZIndex", "LocalTransparencyModifier",

        -- Weld properties
        "Part0", "Part1", "C0", "C1", "Enabled",

        -- Motor6D properties
        "Transform", "CurrentAngle", "DesiredAngle", "MaxVelocity",

        -- Attachment properties
        "WorldPosition", "WorldAxis", "WorldSecondaryAxis", "WorldOrientation", "Axis", "SecondaryAxis",

        -- Constraint properties
        "Attachment0", "Attachment1", "LimitsEnabled", "UpperLimit", "LowerLimit", "Restitution", "Stiffness", "Damping",

        -- Tool properties
        "Enabled", "CanBeDropped", "ManualActivationOnly", "RequiresHandle", "ToolTip", "Grip", "GripForward",
        "GripPos", "GripRight", "GripUp",

        -- Workspace properties
        "CurrentCamera", "DistributedGameTime", "Gravity", "FallenPartsDestroyHeight", "StreamingEnabled",
        "StreamingMinRadius", "StreamingTargetRadius", "AllowThirdPartySales", "FilteringEnabled",

        -- Lighting properties
        "Ambient", "Brightness", "ColorShift_Bottom", "ColorShift_Top", "EnvironmentDiffuseScale",
        "EnvironmentSpecularScale", "GlobalShadows", "OutdoorAmbient", "ShadowSoftness", "ClockTime",
        "GeographicLatitude", "TimeOfDay", "FogColor", "FogEnd", "FogStart", "SunAngularSize", "SunSize",

        -- Team properties
        "TeamColor", "AutoAssignable",

        -- Player properties
        "UserId", "AccountAge", "Character", "CharacterAppearanceId", "FollowUserId", "Neutral", "Team",
        "TeamColor", "AutoJumpEnabled", "CameraMaxZoomDistance", "CameraMinZoomDistance", "CameraMode",
        "CanLoadCharacterAppearance", "CharacterAppearanceLoaded", "DevCameraOcclusionMode", "DevComputerCameraMode",
        "DevComputerMovementMode", "DevEnableMouseLock", "DevTouchCameraMode", "DevTouchMovementMode",
        "GameplayPaused", "HealthDisplayDistance", "MaximumSimulationRadius", "MembershipType", "NameDisplayDistance",
        "ReplicationFocus", "RespawnLocation", "SimulationRadius",

        -- And many more...
        "AbsolutePosition", "AbsoluteSize", "AbsoluteRotation", "GuiState", "SelectionImageObject",
        "BorderPixelSize", "CornerRadius", "Draggable", "Interactable", "MouseButton1Click", "MouseButton1Down",
        "MouseButton1Up", "MouseButton2Click", "MouseButton2Down", "MouseButton2Up", "MouseEnter", "MouseLeave",
        "MouseMoved", "MouseWheelBackward", "MouseWheelForward", "SelectionGained", "SelectionLost", "TouchLongPress",
        "TouchPan", "TouchPinch", "TouchRotate", "TouchSwipe", "TouchTap", "InputBegan", "InputChanged", "InputEnded"
    }

    -- Try to get EVERY SINGLE property
    for _, prop in pairs(allPossibleProps) do
        local success, value = pcall(function()
            local val = obj[prop]
            if val ~= nil then
                -- Special handling for different types
                if typeof(val) == "Vector3" then
                    return "{" .. val.X .. ", " .. val.Y .. ", " .. val.Z .. "}"
                elseif typeof(val) == "CFrame" then
                    return "CFrame.new(" .. val.X .. ", " .. val.Y .. ", " .. val.Z .. ")"
                elseif typeof(val) == "Color3" then
                    return "Color3.new(" .. val.R .. ", " .. val.G .. ", " .. val.B .. ")"
                elseif typeof(val) == "UDim2" then
                    return "UDim2.new(" .. val.X.Scale .. ", " .. val.X.Offset .. ", " .. val.Y.Scale .. ", " .. val.Y.Offset .. ")"
                elseif typeof(val) == "Instance" then
                    return val.Name .. " (" .. val.ClassName .. ")"
                else
                    return tostring(val)
                end
            end
            return nil
        end)

        if success and value ~= nil then
            properties[prop] = value
        end
    end

    return properties
end

-- ULTIMATE function to serialize object data with ABSOLUTELY EVERYTHING
local function serializeObject(obj, depth)
    depth = depth or 0
    objectCount = objectCount + 1

    -- Safety checks (but allow MUCH deeper scanning)
    if objectCount > maxObjects then
        return "-- SAFETY LIMIT REACHED: " .. maxObjects .. " objects scanned\n"
    end

    local result = ""
    local indent = string.rep("│ ", depth)

    -- Create a detailed object header with full path
    local fullPath = obj.Name
    local parent = obj.Parent
    local pathParts = {}

    -- Build full path
    while parent and parent ~= game do
        table.insert(pathParts, 1, parent.Name)
        parent = parent.Parent
    end

    if #pathParts > 0 then
        fullPath = table.concat(pathParts, ".") .. "." .. obj.Name
    end

    -- Beautiful object header
    result = result .. indent .. "┌" .. string.rep("─", 80) .. "┐\n"
    result = result .. indent .. "│ 🎯 OBJECT: " .. obj.Name .. " (" .. obj.ClassName .. ")" .. string.rep(" ", 80 - 12 - string.len(obj.Name) - string.len(obj.ClassName) - 4) .. "│\n"
    result = result .. indent .. "│ 📍 PATH: " .. fullPath .. string.rep(" ", 80 - 9 - string.len(fullPath)) .. "│\n"
    result = result .. indent .. "│ 🔢 DEPTH: " .. depth .. " levels deep" .. string.rep(" ", 80 - 10 - string.len(tostring(depth)) - 12) .. "│\n"
    result = result .. indent .. "├" .. string.rep("─", 80) .. "┤\n"

    -- Get ALL properties
    local properties = getAllProperties(obj)
    local propCount = 0
    for _ in pairs(properties) do propCount = propCount + 1 end

    result = result .. indent .. "│ 📊 PROPERTIES (" .. propCount .. " found):" .. string.rep(" ", 80 - 15 - string.len(tostring(propCount)) - 8) .. "│\n"
    result = result .. indent .. "├" .. string.rep("─", 80) .. "┤\n"

    -- Display ALL properties in organized way
    for propName, propValue in pairs(properties) do
        local propLine = "│ ▶ " .. propName .. ": " .. tostring(propValue)
        if string.len(propLine) > 79 then
            propLine = string.sub(propLine, 1, 76) .. "..."
        end
        result = result .. indent .. propLine .. string.rep(" ", 80 - string.len(propLine)) .. "│\n"
    end

    -- Get children information
    local children = obj:GetChildren()
    local childrenCount = #children

    if childrenCount > 0 then
        result = result .. indent .. "├" .. string.rep("─", 80) .. "┤\n"
        result = result .. indent .. "│ 👶 CHILDREN (" .. childrenCount .. " found):" .. string.rep(" ", 80 - 13 - string.len(tostring(childrenCount)) - 8) .. "│\n"

        -- List all children names and types
        for i, child in pairs(children) do
            local childInfo = "│   " .. i .. ". " .. child.Name .. " (" .. child.ClassName .. ")"
            if string.len(childInfo) > 79 then
                childInfo = string.sub(childInfo, 1, 76) .. "..."
            end
            result = result .. indent .. childInfo .. string.rep(" ", 80 - string.len(childInfo)) .. "│\n"
        end
    end

    result = result .. indent .. "└" .. string.rep("─", 80) .. "┘\n\n"

    return result
end

-- ULTIMATE function to scan service with INFINITE DEPTH - EVERYTHING!
local function scanService(service, serviceName)
    local data = "\n" .. string.rep("█", 100) .. "\n"
    data = data .. "█" .. string.rep(" ", 98) .. "█\n"
    data = data .. "█" .. string.rep(" ", 30) .. "🔍 DEEP SCANNING: " .. serviceName .. string.rep(" ", 50 - string.len(serviceName)) .. "█\n"
    data = data .. "█" .. string.rep(" ", 98) .. "█\n"
    data = data .. string.rep("█", 100) .. "\n\n"

    local function scanChildren(parent, depth)
        depth = depth or 0
        local childData = ""

        -- Get all children
        local children = parent:GetChildren()

        for i, child in pairs(children) do
            if objectCount > maxObjects then
                childData = childData .. string.rep("│ ", depth) .. "⚠️ STOPPING: Object limit reached (" .. maxObjects .. ")\n"
                break
            end

            -- Serialize this object with ALL its properties
            childData = childData .. serializeObject(child, depth)

            -- 🚀 INFINITE RECURSION - Go into EVERY child, no matter how deep!
            local grandChildren = child:GetChildren()
            if #grandChildren > 0 then
                childData = childData .. string.rep("│ ", depth) .. "🔽 DIVING INTO " .. child.Name .. " → " .. #grandChildren .. " children found:\n"
                childData = childData .. string.rep("│ ", depth) .. "┌" .. string.rep("─", 50) .. "┐\n"

                -- RECURSIVELY scan ALL children - NO LIMITS!
                childData = childData .. scanChildren(child, depth + 1)

                childData = childData .. string.rep("│ ", depth) .. "└" .. string.rep("─", 50) .. "┘\n"
                childData = childData .. string.rep("│ ", depth) .. "🔼 FINISHED SCANNING " .. child.Name .. "\n\n"
            end

            -- Add visual separator between siblings
            if i < #children then
                childData = childData .. string.rep("│ ", depth) .. "┃\n"
            end
        end

        return childData
    end

    -- Add service header info
    data = data .. "🎯 SERVICE ROOT OBJECT:\n"
    data = data .. serializeObject(service, 0)

    -- Start the infinite deep scan
    local children = service:GetChildren()
    if #children > 0 then
        data = data .. "\n🚀 STARTING INFINITE DEEP SCAN OF " .. #children .. " ROOT CHILDREN:\n"
        data = data .. "═══════════════════════════════════════════════════════════════════════════════════════════════════\n\n"
        data = data .. scanChildren(service, 1)
    else
        data = data .. "\n📭 NO CHILDREN FOUND IN " .. serviceName .. "\n"
    end

    return data
end

-- Enhanced services to scan - includes ALL possible services
local servicesToScan = {
    {game.Workspace, "Workspace"},
    {game.Players, "Players"},
    {game.ReplicatedStorage, "ReplicatedStorage"},
    {game.ReplicatedFirst, "ReplicatedFirst"},
    {game.ServerStorage, "ServerStorage"},
    {game.ServerScriptService, "ServerScriptService"},
    {game.StarterGui, "StarterGui"},
    {game.StarterPack, "StarterPack"},
    {game.StarterPlayer, "StarterPlayer"},
    {game.SoundService, "SoundService"},
    {game.Lighting, "Lighting"},
    {game.MaterialService, "MaterialService"},
    {game.Teams, "Teams"},
    {game.Chat, "Chat"},
    {game.LocalizationService, "LocalizationService"},
    {game.MarketplaceService, "MarketplaceService"},
    {game.TeleportService, "TeleportService"},
    {game.BadgeService, "BadgeService"},
    {game.GamePassService, "GamePassService"},
    {game.DataStoreService, "DataStoreService"},
    {game.MessagingService, "MessagingService"},
    {game.HttpService, "HttpService"},
    {game.TweenService, "TweenService"},
    {game.RunService, "RunService"},
    {game.UserInputService, "UserInputService"},
    {game.ContextActionService, "ContextActionService"},
    {game.GuiService, "GuiService"},
    {game.PathfindingService, "PathfindingService"},
    {game.PhysicsService, "PhysicsService"},
    {game.Debris, "Debris"},
    {game.InsertService, "InsertService"},
    {game.CollectionService, "CollectionService"},
    {game.Selection, "Selection"},
    {game.ChangeHistoryService, "ChangeHistoryService"},
    {game.CoreGui, "CoreGui"},
    {game.StarterPlayerScripts, "StarterPlayerScripts"},
    {game.JointsService, "JointsService"}
}

-- Enhanced main scanning function with detailed progress
local function startScan()
    local totalServices = #servicesToScan
    local currentService = 0

    -- Reset object counter
    objectCount = 0

    local function scanNext()
        currentService = currentService + 1

        if currentService > totalServices then
            local endTime = tick()
            local totalTime = endTime - startTime
            progressText.Text = "🎉 ULTIMATE SCAN COMPLETE! 🎉"
            progressBar.BackgroundColor3 = Color3.fromRGB(0, 255, 0)
            print("🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥")
            print("� ULTIMATE DEEP SCAN COMPLETED! EVERYTHING EXTRACTED! �")
            print("📊 Total objects scanned: " .. objectCount)
            print("🔧 Total services scanned: " .. totalServices)
            print("📋 Total clipboard size: " .. string.len(accumulatedData) .. " characters")
            print("⏱️ Total scan time: " .. math.floor(totalTime) .. " seconds")
            print("🚀 EVERY SINGLE PROPERTY OF EVERY OBJECT HAS BEEN COPIED!")
            print("📁 ALL NESTED FOLDERS, TYCOONS, AND SUBFOLDERS INCLUDED!")
            print("🔍 INFINITE DEPTH SCANNING SUCCESSFUL!")
            print("🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥")
            wait(5)
            screenGui:Destroy()
            return
        end

        local service, serviceName = servicesToScan[currentService][1], servicesToScan[currentService][2]
        progressText.Text = "Deep scanning " .. serviceName .. "... (" .. currentService .. "/" .. totalServices .. ")"

        -- Update progress bar
        local progress = currentService / totalServices
        local tween = TweenService:Create(progressBar, TweenInfo.new(0.3), {Size = UDim2.new(progress, 0, 1, 0)})
        tween:Play()

        -- Scan service with enhanced error handling
        local success, data = pcall(function()
            local startObjects = objectCount
            local result = scanService(service, serviceName)
            local objectsInService = objectCount - startObjects
            print("✓ " .. serviceName .. " scanned: " .. objectsInService .. " objects found")
            return result
        end)

        if success then
            -- Stack the new data with existing clipboard content
            if accumulatedData == "" then
                accumulatedData = "�🔥🔥 ULTIMATE ROBLOX GAME INFINITE DEPTH SCANNER RESULTS �🔥🔥\n"
                accumulatedData = accumulatedData .. "🎯 EXTRACTS ABSOLUTELY EVERYTHING - INFINITE DEPTH SCANNING\n"
                accumulatedData = accumulatedData .. "📅 Generated: " .. os.date() .. "\n"
                accumulatedData = accumulatedData .. "🔢 Max Objects Limit: " .. maxObjects .. "\n"
                accumulatedData = accumulatedData .. "📊 Total Services: " .. totalServices .. "\n"
                accumulatedData = accumulatedData .. "🚀 Scans: Workspace→Tycoons→TycoonFolders→SubFolders→Everything!\n"
                accumulatedData = accumulatedData .. string.rep("🔥", 100) .. "\n\n"
                accumulatedData = accumulatedData .. data
            else
                accumulatedData = accumulatedData .. separator .. data
            end

            -- Copy accumulated data to clipboard
            setclipboard(accumulatedData)
            print("📋 ✅ " .. serviceName .. " FULLY EXTRACTED → Clipboard (Service " .. currentService .. "/" .. totalServices .. ") | Objects: " .. objectCount)
        else
            print("❌ Failed to scan " .. serviceName .. ": " .. tostring(data))
        end

        -- Shorter wait time but with safety check
        wait(0.5)

        -- Check if we're approaching object limit
        if objectCount > maxObjects * 0.8 then
            print("⚠️  WARNING: Approaching object limit (" .. objectCount .. "/" .. maxObjects .. ")")
        end

        scanNext()
    end

    scanNext()
end

-- Start the enhanced scan
print("🚀 ENHANCED DEEP SCANNER STARTING...")
print("📊 Will scan " .. #servicesToScan .. " services")
print("🔍 No depth limit - will scan EVERYTHING")
print("📋 Results will be progressively copied to clipboard")
print("⚠️  Safety limit: " .. maxObjects .. " objects max")
print("👀 Check top-right corner for progress!")
print(string.rep("═", 50))

startScan()