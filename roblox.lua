-- Roblox Game Scanner Script
-- Scans and copies game information to clipboard progressively with stacking functionality

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")

-- Variable to store accumulated clipboard data
local accumulatedData = ""
local separator = "\n\n" .. string.rep("=", 50) .. "\n\n"

-- Create progress indicator GUI
local screenGui = Instance.new("ScreenGui")
screenGui.Name = "GameScannerGUI"
screenGui.Parent = Players.LocalPlayer:WaitForChild("PlayerGui")

local progressFrame = Instance.new("Frame")
progressFrame.Size = UDim2.new(0, 200, 0, 20)
progressFrame.Position = UDim2.new(1, -220, 0, 20)
progressFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
progressFrame.BorderSizePixel = 0
progressFrame.Parent = screenGui

local progressBar = Instance.new("Frame")
progressBar.Size = UDim2.new(0, 0, 1, 0)
progressBar.Position = UDim2.new(0, 0, 0, 0)
progressBar.BackgroundColor3 = Color3.fromRGB(0, 255, 100)
progressBar.BorderSizePixel = 0
progressBar.Parent = progressFrame

local progressText = Instance.new("TextLabel")
progressText.Size = UDim2.new(1, 0, 1, 0)
progressText.Position = UDim2.new(0, 0, 0, 0)
progressText.BackgroundTransparency = 1
progressText.Text = "Starting scan..."
progressText.TextColor3 = Color3.fromRGB(255, 255, 255)
progressText.TextScaled = true
progressText.Font = Enum.Font.SourceSansBold
progressText.Parent = progressFrame

-- Function to serialize object data
local function serializeObject(obj, depth)
    depth = depth or 0
    if depth > 5 then return "-- Max depth reached" end
    
    local result = ""
    local indent = string.rep("  ", depth)
    
    result = result .. indent .. "-- " .. obj.Name .. " (" .. obj.ClassName .. ")\n"
    
    -- Get properties
    local success, properties = pcall(function()
        local props = {}
        for _, prop in pairs({"Name", "ClassName", "Parent"}) do
            if obj[prop] then
                table.insert(props, prop .. ": " .. tostring(obj[prop]))
            end
        end
        return props
    end)
    
    if success then
        for _, prop in pairs(properties) do
            result = result .. indent .. "-- " .. prop .. "\n"
        end
    end
    
    return result
end

-- Function to scan service
local function scanService(service, serviceName)
    local data = "-- === " .. serviceName .. " ===\n"
    
    local function scanChildren(parent, depth)
        depth = depth or 0
        if depth > 3 then return "" end
        
        local childData = ""
        for _, child in pairs(parent:GetChildren()) do
            childData = childData .. serializeObject(child, depth)
            if #child:GetChildren() > 0 then
                childData = childData .. scanChildren(child, depth + 1)
            end
        end
        return childData
    end
    
    data = data .. scanChildren(service)
    return data
end

-- Services to scan
local servicesToScan = {
    {game.Workspace, "Workspace"},
    {game.Players, "Players"},
    {game.ReplicatedStorage, "ReplicatedStorage"},
    {game.ReplicatedFirst, "ReplicatedFirst"},
    {game.ServerStorage, "ServerStorage"},
    {game.ServerScriptService, "ServerScriptService"},
    {game.StarterGui, "StarterGui"},
    {game.StarterPack, "StarterPack"},
    {game.StarterPlayer, "StarterPlayer"},
    {game.SoundService, "SoundService"},
    {game.Lighting, "Lighting"},
    {game.MaterialService, "MaterialService"},
    {game.Teams, "Teams"}
}

-- Main scanning function
local function startScan()
    local totalServices = #servicesToScan
    local currentService = 0
    
    local function scanNext()
        currentService = currentService + 1
        
        if currentService > totalServices then
            progressText.Text = "Scan Complete!"
            progressBar.BackgroundColor3 = Color3.fromRGB(0, 255, 0)
            print("All scans complete! Full stacked data is now in clipboard.")
            print("Total clipboard size: " .. string.len(accumulatedData) .. " characters")
            wait(2)
            screenGui:Destroy()
            return
        end
        
        local service, serviceName = servicesToScan[currentService][1], servicesToScan[currentService][2]
        progressText.Text = "Scanning " .. serviceName .. "..."
        
        -- Update progress bar
        local progress = currentService / totalServices
        local tween = TweenService:Create(progressBar, TweenInfo.new(0.3), {Size = UDim2.new(progress, 0, 1, 0)})
        tween:Play()
        
        -- Scan service
        local success, data = pcall(function()
            return scanService(service, serviceName)
        end)
        
        if success then
            -- Stack the new data with existing clipboard content
            if accumulatedData == "" then
                accumulatedData = data
            else
                accumulatedData = accumulatedData .. separator .. data
            end
            
            -- Copy accumulated data to clipboard
            setclipboard(accumulatedData)
            print("Added " .. serviceName .. " data to clipboard stack (Total services: " .. currentService .. ")")
        else
            print("Failed to scan " .. serviceName)
        end
        
        -- Wait before next scan to avoid crashes
        wait(1)
        scanNext()
    end
    
    scanNext()
end

-- Start the scan
startScan()

print("Game scanner started! Check top-right corner for progress.")