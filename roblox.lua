-- Enhanced Roblox Game Scanner Script
-- Deep scans and copies ALL game information to clipboard progressively with maximum detail extraction
-- Goes infinitely deep into all objects and extracts ALL properties

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")
local HttpService = game:GetService("HttpService")

-- Variable to store accumulated clipboard data
local accumulatedData = ""
local separator = "\n\n" .. string.rep("=", 50) .. "\n\n"
local objectCount = 0
local maxObjects = 50000 -- Safety limit to prevent infinite loops

-- Create progress indicator GUI
local screenGui = Instance.new("ScreenGui")
screenGui.Name = "GameScannerGUI"
screenGui.Parent = Players.LocalPlayer:WaitForChild("PlayerGui")

local progressFrame = Instance.new("Frame")
progressFrame.Size = UDim2.new(0, 200, 0, 20)
progressFrame.Position = UDim2.new(1, -220, 0, 20)
progressFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
progressFrame.BorderSizePixel = 0
progressFrame.Parent = screenGui

local progressBar = Instance.new("Frame")
progressBar.Size = UDim2.new(0, 0, 1, 0)
progressBar.Position = UDim2.new(0, 0, 0, 0)
progressBar.BackgroundColor3 = Color3.fromRGB(0, 255, 100)
progressBar.BorderSizePixel = 0
progressBar.Parent = progressFrame

local progressText = Instance.new("TextLabel")
progressText.Size = UDim2.new(1, 0, 1, 0)
progressText.Position = UDim2.new(0, 0, 0, 0)
progressText.BackgroundTransparency = 1
progressText.Text = "Starting scan..."
progressText.TextColor3 = Color3.fromRGB(255, 255, 255)
progressText.TextScaled = true
progressText.Font = Enum.Font.SourceSansBold
progressText.Parent = progressFrame

-- Function to get all possible properties of an object
local function getAllProperties(obj)
    local properties = {}

    -- Common properties that most objects have
    local commonProps = {
        "Name", "ClassName", "Parent", "Archivable", "RobloxLocked"
    }

    -- Instance-specific properties based on class
    local classSpecificProps = {
        Part = {"Position", "Size", "Rotation", "Material", "BrickColor", "Color", "Transparency", "Reflectance", "CanCollide", "Anchored", "Shape", "TopSurface", "BottomSurface", "LeftSurface", "RightSurface", "FrontSurface", "BackSurface"},
        MeshPart = {"Position", "Size", "Rotation", "Material", "BrickColor", "Color", "Transparency", "Reflectance", "CanCollide", "Anchored", "MeshId", "TextureID"},
        Decal = {"Texture", "Face", "Transparency", "Color3"},
        SurfaceGui = {"Face", "CanvasSize", "LightInfluence", "Brightness"},
        ScreenGui = {"DisplayOrder", "IgnoreGuiInset", "ResetOnSpawn", "ZIndexBehavior"},
        Frame = {"Size", "Position", "BackgroundColor3", "BackgroundTransparency", "BorderColor3", "BorderSizePixel", "Rotation", "Visible", "ZIndex"},
        TextLabel = {"Text", "TextColor3", "TextSize", "Font", "TextWrapped", "TextScaled", "TextStrokeTransparency", "TextStrokeColor3"},
        TextButton = {"Text", "TextColor3", "TextSize", "Font", "AutoButtonColor"},
        ImageLabel = {"Image", "ImageColor3", "ImageTransparency", "ScaleType", "SliceCenter"},
        ScrollingFrame = {"ScrollBarThickness", "ScrollBarImageColor3", "CanvasSize", "ScrollingDirection"},
        LocalScript = {"Source", "Disabled"},
        Script = {"Source", "Disabled"},
        ModuleScript = {"Source"},
        RemoteEvent = {},
        RemoteFunction = {},
        BindableEvent = {},
        BindableFunction = {},
        Sound = {"SoundId", "Volume", "Pitch", "PlaybackSpeed", "Looped", "Playing", "TimePosition", "EmitterSize"},
        PointLight = {"Brightness", "Color", "Range", "Shadows"},
        SpotLight = {"Brightness", "Color", "Range", "Angle", "Face", "Shadows"},
        SurfaceLight = {"Brightness", "Color", "Range", "Angle", "Face", "Shadows"},
        Camera = {"CameraType", "FieldOfView", "Focus", "CFrame"},
        Humanoid = {"Health", "MaxHealth", "WalkSpeed", "JumpPower", "DisplayDistanceType", "HealthDisplayDistance", "NameDisplayDistance"},
        Model = {"PrimaryPart"},
        Folder = {},
        Configuration = {},
        StringValue = {"Value"},
        IntValue = {"Value"},
        NumberValue = {"Value"},
        BoolValue = {"Value"},
        Vector3Value = {"Value"},
        CFrameValue = {"Value"},
        Color3Value = {"Value"},
        ObjectValue = {"Value"},
        RayValue = {"Value"}
    }

    -- Start with common properties
    for _, prop in pairs(commonProps) do
        local success, value = pcall(function() return obj[prop] end)
        if success and value ~= nil then
            properties[prop] = tostring(value)
        end
    end

    -- Add class-specific properties
    local className = obj.ClassName
    if classSpecificProps[className] then
        for _, prop in pairs(classSpecificProps[className]) do
            local success, value = pcall(function() return obj[prop] end)
            if success and value ~= nil then
                properties[prop] = tostring(value)
            end
        end
    end

    -- Try to get additional properties dynamically
    local success, _ = pcall(function()
        -- Try common properties that might exist
        local additionalProps = {
            "CFrame", "Orientation", "Velocity", "AssemblyLinearVelocity", "AssemblyAngularVelocity",
            "Texture", "Decal", "SurfaceType", "JoinSurface", "Elasticity", "Friction",
            "CustomPhysicalProperties", "AssemblyMass", "AssemblyRootPart", "AssemblyCenterOfMass"
        }

        for _, prop in pairs(additionalProps) do
            if not properties[prop] then
                local success2, value = pcall(function() return obj[prop] end)
                if success2 and value ~= nil then
                    properties[prop] = tostring(value)
                end
            end
        end
    end)

    return properties
end

-- Enhanced function to serialize object data with ALL properties
local function serializeObject(obj, depth)
    depth = depth or 0
    objectCount = objectCount + 1

    -- Safety checks
    if objectCount > maxObjects then
        return "-- SAFETY LIMIT REACHED: Too many objects scanned\n"
    end
    if depth > 20 then
        return "-- MAX DEPTH REACHED (20 levels deep)\n"
    end

    local result = ""
    local indent = string.rep("  ", depth)

    -- Object header
    result = result .. indent .. "╔══ " .. obj.Name .. " (" .. obj.ClassName .. ") ══\n"

    -- Get ALL properties
    local properties = getAllProperties(obj)

    -- Display all properties
    for propName, propValue in pairs(properties) do
        result = result .. indent .. "║ " .. propName .. ": " .. propValue .. "\n"
    end

    -- Get children count
    local childrenCount = #obj:GetChildren()
    if childrenCount > 0 then
        result = result .. indent .. "║ Children: " .. childrenCount .. "\n"
    end

    result = result .. indent .. "╚" .. string.rep("═", 30) .. "\n"

    return result
end

-- Enhanced function to scan service with infinite depth
local function scanService(service, serviceName)
    local data = "\n" .. string.rep("█", 60) .. "\n"
    data = data .. "█" .. string.rep(" ", 58) .. "█\n"
    data = data .. "█" .. string.rep(" ", 20) .. serviceName .. string.rep(" ", 38 - string.len(serviceName)) .. "█\n"
    data = data .. "█" .. string.rep(" ", 58) .. "█\n"
    data = data .. string.rep("█", 60) .. "\n\n"

    local function scanChildren(parent, depth)
        depth = depth or 0
        local childData = ""

        -- Get all children
        local children = parent:GetChildren()

        for i, child in pairs(children) do
            if objectCount > maxObjects then
                childData = childData .. string.rep("  ", depth) .. "-- STOPPING: Object limit reached\n"
                break
            end

            -- Serialize this object with all its properties
            childData = childData .. serializeObject(child, depth)

            -- Recursively scan children (NO DEPTH LIMIT - goes infinite)
            local grandChildren = child:GetChildren()
            if #grandChildren > 0 then
                childData = childData .. string.rep("  ", depth) .. "├─ CHILDREN OF " .. child.Name .. " (" .. #grandChildren .. " items):\n"
                childData = childData .. scanChildren(child, depth + 1)
            end

            -- Add separator between siblings
            if i < #children then
                childData = childData .. string.rep("  ", depth) .. "│\n"
            end
        end

        return childData
    end

    -- Add service info
    data = data .. serializeObject(service, 0)
    data = data .. "\n" .. "ROOT CHILDREN:\n"
    data = data .. scanChildren(service, 1)

    return data
end

-- Enhanced services to scan - includes ALL possible services
local servicesToScan = {
    {game.Workspace, "Workspace"},
    {game.Players, "Players"},
    {game.ReplicatedStorage, "ReplicatedStorage"},
    {game.ReplicatedFirst, "ReplicatedFirst"},
    {game.ServerStorage, "ServerStorage"},
    {game.ServerScriptService, "ServerScriptService"},
    {game.StarterGui, "StarterGui"},
    {game.StarterPack, "StarterPack"},
    {game.StarterPlayer, "StarterPlayer"},
    {game.SoundService, "SoundService"},
    {game.Lighting, "Lighting"},
    {game.MaterialService, "MaterialService"},
    {game.Teams, "Teams"},
    {game.Chat, "Chat"},
    {game.LocalizationService, "LocalizationService"},
    {game.MarketplaceService, "MarketplaceService"},
    {game.TeleportService, "TeleportService"},
    {game.BadgeService, "BadgeService"},
    {game.GamePassService, "GamePassService"},
    {game.DataStoreService, "DataStoreService"},
    {game.MessagingService, "MessagingService"},
    {game.HttpService, "HttpService"},
    {game.TweenService, "TweenService"},
    {game.RunService, "RunService"},
    {game.UserInputService, "UserInputService"},
    {game.ContextActionService, "ContextActionService"},
    {game.GuiService, "GuiService"},
    {game.PathfindingService, "PathfindingService"},
    {game.PhysicsService, "PhysicsService"},
    {game.Debris, "Debris"},
    {game.InsertService, "InsertService"},
    {game.CollectionService, "CollectionService"},
    {game.Selection, "Selection"},
    {game.ChangeHistoryService, "ChangeHistoryService"},
    {game.CoreGui, "CoreGui"},
    {game.StarterPlayerScripts, "StarterPlayerScripts"},
    {game.JointsService, "JointsService"}
}

-- Enhanced main scanning function with detailed progress
local function startScan()
    local totalServices = #servicesToScan
    local currentService = 0

    -- Reset object counter
    objectCount = 0

    local function scanNext()
        currentService = currentService + 1

        if currentService > totalServices then
            progressText.Text = "SCAN COMPLETE!"
            progressBar.BackgroundColor3 = Color3.fromRGB(0, 255, 0)
            print("═══════════════════════════════════════")
            print("🎉 ALL SCANS COMPLETE! 🎉")
            print("Total objects scanned: " .. objectCount)
            print("Total services scanned: " .. totalServices)
            print("Total clipboard size: " .. string.len(accumulatedData) .. " characters")
            print("Full deep-scan data is now in clipboard!")
            print("═══════════════════════════════════════")
            wait(3)
            screenGui:Destroy()
            return
        end

        local service, serviceName = servicesToScan[currentService][1], servicesToScan[currentService][2]
        progressText.Text = "Deep scanning " .. serviceName .. "... (" .. currentService .. "/" .. totalServices .. ")"

        -- Update progress bar
        local progress = currentService / totalServices
        local tween = TweenService:Create(progressBar, TweenInfo.new(0.3), {Size = UDim2.new(progress, 0, 1, 0)})
        tween:Play()

        -- Scan service with enhanced error handling
        local success, data = pcall(function()
            local startObjects = objectCount
            local result = scanService(service, serviceName)
            local objectsInService = objectCount - startObjects
            print("✓ " .. serviceName .. " scanned: " .. objectsInService .. " objects found")
            return result
        end)

        if success then
            -- Stack the new data with existing clipboard content
            if accumulatedData == "" then
                accumulatedData = "🔍 ENHANCED ROBLOX GAME DEEP SCANNER RESULTS 🔍\n"
                accumulatedData = accumulatedData .. "Generated: " .. os.date() .. "\n"
                accumulatedData = accumulatedData .. "Max Objects Limit: " .. maxObjects .. "\n"
                accumulatedData = accumulatedData .. string.rep("═", 80) .. "\n\n"
                accumulatedData = accumulatedData .. data
            else
                accumulatedData = accumulatedData .. separator .. data
            end

            -- Copy accumulated data to clipboard
            setclipboard(accumulatedData)
            print("📋 Added " .. serviceName .. " to clipboard stack (Service " .. currentService .. "/" .. totalServices .. ")")
        else
            print("❌ Failed to scan " .. serviceName .. ": " .. tostring(data))
        end

        -- Shorter wait time but with safety check
        wait(0.5)

        -- Check if we're approaching object limit
        if objectCount > maxObjects * 0.8 then
            print("⚠️  WARNING: Approaching object limit (" .. objectCount .. "/" .. maxObjects .. ")")
        end

        scanNext()
    end

    scanNext()
end

-- Start the scan
startScan()

print("Game scanner started! Check top-right corner for progress.")